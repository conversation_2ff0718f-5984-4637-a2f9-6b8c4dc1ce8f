{"name": "app", "private": true, "version": "0.0.1", "type": "module", "engines": {"node": "20"}, "scripts": {"dev": "vite dev --host", "build": "npm run check:ts && npm run check && vite build", "preview": "vite preview", "check:ts": "tsc --noEmit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "lint": "eslint ."}, "devDependencies": {"@eslint/compat": "1.2.3", "@sveltejs/adapter-auto": "3.0.0", "@sveltejs/kit": "2.9.0", "@sveltejs/vite-plugin-svelte": "5.0.0", "@types/aos": "3.0.7", "@types/locomotive-scroll": "4.1.4", "autoprefixer": "10.4.20", "eslint": "9.7.0", "eslint-plugin-svelte": "2.36.0", "globals": "15.0.0", "sass-embedded": "1.83.0", "svelte": "5.0.0", "svelte-check": "4.0.0", "tailwindcss": "3.4.9", "typescript": "5.0.0", "typescript-eslint": "8.0.0", "vite": "6.0.0"}, "dependencies": {"@aws-sdk/client-s3": "3.712.0", "@aws-sdk/lib-storage": "3.712.0", "@aws-sdk/s3-request-presigner": "3.712.0", "@googlemaps/js-api-loader": "1.16.8", "@googlemaps/markerclusterer": "2.5.3", "@portabletext/svelte": "2.1.11", "@sanity/client": "6.24.1", "@selemondev/svelte-marquee": "0.0.2", "@splidejs/svelte-splide": "0.2.9", "aos": "2.3.4", "apexcharts": "4.2.0", "aws-amplify": "6.10.3", "axios": "1.7.9", "clone": "2.1.2", "firebase": "11.1.0", "flowbite": "2.5.2", "flowbite-svelte": "0.47.4", "flowbite-svelte-icons": "2.0.2", "jspdf": "2.5.2", "locomotive-scroll": "4.1.4", "nodemailer": "6.9.16", "notyf": "3.10.0", "uuid": "11.0.3", "zod": "3.24.1"}}