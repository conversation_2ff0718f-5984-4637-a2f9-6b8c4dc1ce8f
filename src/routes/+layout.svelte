<script lang="ts">
    import "./static/app.css";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import "../app.css";
    import { loggedInUser, userPermissions } from "$lib/common/utils/store";
    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { onAuthStateChanged, signOut } from "firebase/auth";
    import { usiFirebaseAuth } from "../firebaseInit";
    import { showErrorToast } from "$lib/common/utils/common-utils";

    let isLoading: boolean = true;

    const checkUserLoggedInStatus = () => {
        onAuthStateChanged(usiFirebaseAuth, async (user) => {
            try {
                if (!user) {
                    goto("/signin");
                } else {
                    const result: any = await PresenterProvider.userPresenter.getByFirebaseToken();

                    if (result.success) {
                        const permissions = await PresenterProvider.userRolePresenter.getById(
                            result.data!.role.id
                        );
                        if (!permissions.success) {
                            await signOut(usiFirebaseAuth);
                            goto("/signin");
                            showErrorToast(result.message);
                            return;
                        }
                        userPermissions.update(() => permissions.data.permissions);

                        loggedInUser.update(() => result.data);
                        if (
                            $page.url.pathname === "/" ||
                            $page.url.pathname === "" ||
                            $page.url.pathname === "/signin" ||
                            $page.url.pathname === "/admin"
                        ) {
                            goto("/admin/purchase-invoices");
                        }
                    } else {
                        await signOut(usiFirebaseAuth);
                        goto("/signin");
                        showErrorToast(result.message);
                    }
                }
            } catch (error) {
            } finally {
                isLoading = false;
            }
        });
    };

    onMount(async () => {
        checkUserLoggedInStatus();
    });
</script>

<div class="absolute z-[50] bg-red-600"></div>
{#if isLoading}
    <PageLoader />
{:else}
    <slot />
{/if}
