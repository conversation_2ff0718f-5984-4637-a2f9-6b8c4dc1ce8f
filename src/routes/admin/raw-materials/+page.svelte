<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import RawMaterialTable from "$lib/raw_material/components/RawMaterialTable.svelte";
    import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    let selectedRowsMap: Map<number, IRawMaterial> = new Map();

    let isLoading = true;

    let paginationData: PaginatedDataWrapper<IRawMaterial> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    const loadData = async (page: number) => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL.READ)) {
            isLoading = false;
            return;
        }

        isLoading = true;
        const response = await PresenterProvider.rawMaterialPresenter.getAll(
            page,
            paginationData.pageSize
        );
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };
    onMount(async () => {
        loadData(1);
    });
</script>
<svelte:head><title>Raw Material</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.READ)}
    <NoPermissionView />
{:else}
    <RawMaterialTable {paginationData} bind:selectedRowsMap />
{/if}
