<script lang="ts">
    import SuccessToast from "$lib/toasts/ui_components/SuccessToast.svelte";
    import ErrorToast from "$lib/toasts/ui_components/ErrorToast.svelte";
    import { sidebarOpen } from "$lib/common/utils/store";

    import Navigation from "$lib/common/components/admin/Navigation.svelte";
    import Sidebar from "$lib/common/components/admin/Sidebar.svelte";
    import { Spinner } from "flowbite-svelte";

    let drawerHidden: boolean = false;
    let width: number;
    let isLoading: boolean = false;
    const toggleDrawer = () => {
        drawerHidden = !drawerHidden;
        sidebarOpen.update(() => drawerHidden);
    };

    // if (
    //     (!isUserLoggedIn && !user) ||
    //     (user && user.[bulkType] !== USER_TYPE.ADMIN && user.status !== USER_STATUS.ACTIVE)
    // ) {
    // goto("/");
    // }
    //
</script>

<svelte:window bind:innerWidth={width} />

{#if isLoading}
    <div class="flex h-[100vh] w-[100vw] items-center justify-center">
        <Spinner size={"8"} color="gray" />
    </div>
{:else}
    <Navigation {toggleDrawer} />
    <div
        class="{drawerHidden
            ? ``
            : `lg:ml-[16rem]`} relative mt-[60px] flex min-h-[calc(100vh-60px)] bg-primary-50 duration-500 ease-in-out dark:bg-primary-800"
    >
        <main class=" mx-auto w-full overflow-y-auto px-[3%] py-[4%]">
            <slot />
        </main>
    </div>
    <ErrorToast />
    <SuccessToast />
    <Sidebar bind:drawerHidden {width} />
{/if}
