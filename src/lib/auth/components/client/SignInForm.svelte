<script lang="ts">
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import Loader from "$lib/common/components/Loader.svelte";
    import {
        signInWithEmailAndPassword,
        AuthErrorCodes,
        onAuthStateChanged,
        signOut,
    } from "firebase/auth";
    import { usiFirebaseAuth } from "../../../../firebaseInit";
    import { goto } from "$app/navigation";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { RepoProvider } from "$lib/RepoProvider";
    import { onMount } from "svelte";
    import { loggedInUser, userPermissions } from "$lib/common/utils/store";
    import { page } from "$app/stores";

    // States
    let isLoading = false;
    let email = "";
    let password = "";
    let showPassword = false;
    let validationMap = new Map();
    let forgotPass: boolean = false;

    const handleEmailLogin = async () => {
        isLoading = true;

        try {
            await signInWithEmailAndPassword(usiFirebaseAuth, email, password);
        } catch (error: any) {
            isLoading = false;
            if (error.code === AuthErrorCodes.INVALID_LOGIN_CREDENTIALS) {
                showErrorToast("Invalid email or password");
            } else {
                showErrorToast("Something went wrong");
            }
        }
    };

    const handleForgotPassword = async () => {
        isLoading = true;
        const res = await RepoProvider.authProvider.sendForgotPasswordLink(email);

        if (res.success) {
            showSuccessToast(
                "If email is registered, you will recieve an email to reset password!"
            );
            forgotPass = false;
        } else {
            showErrorToast(res.message ?? "Failed to send Email!");
        }
        isLoading = false;
    };

    const togglePasswordVisibility = () => {
        showPassword = !showPassword;
    };
</script>

<div class="m-4 flex w-full">
    <div
        class="mx-auto flex w-full flex-col overflow-hidden rounded-sm shadow-lg dark:bg-gray-800 lg:max-w-4xl"
    >
        <div class="w-full flex justify-center items-center">
            <img class="w-[30%]" src="/images/logo.png" alt="" />
        </div>
        <div class="mt-5"></div>
        <div class="mt-5"></div>
        <div class="flex w-full justify-between px-6 pb-2 md:px-8">
            {#if forgotPass}
                <p class="font-serif text-2xl font-bold">Reset Your Password</p>
            {:else}
                <p class="font-serif text-2xl font-bold">Login to your Account</p>
            {/if}
        </div>

        {#if forgotPass}
            <div class="w-full px-6 pb-8 md:px-8">
                <div class="mt-4">
                    <label
                        class="mb-2 block text-xs font-medium text-white"
                        for="LoggingEmailAddress"
                    >
                        Email
                    </label>
                    <input
                        id="LoggingEmailAddress"
                        placeholder="Email"
                        class="block w-full rounded-lg border bg-white px-4 py-2 text-gray-700 focus:border-blue-400 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-40 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:focus:border-blue-300 {validationMap.has(
                            'email'
                        )
                            ? 'border-red-500'
                            : ''}"
                        type="email"
                        on:input={() => {
                            validationMap = new Map();
                        }}
                        on:change={({ currentTarget }) => {
                            email = currentTarget.value.toLowerCase();
                        }}
                        bind:value={email}
                    />
                </div>

                {#if isLoading}
                    <div class="mt-6">
                        <button
                            class="flex w-full transform items-center justify-center rounded-lg"
                        >
                            <Loader />
                        </button>
                    </div>
                {:else}
                    <div class="mt-6">
                        <button
                            class="w-full font-serif transform rounded-lg bg-gray-800 px-6 py-3 text-sm font-bold capitalize tracking-wide text-white transition-colors duration-300 hover:bg-gray-700 focus:outline-none focus:ring focus:ring-gray-300 focus:ring-opacity-50"
                            on:click={handleForgotPassword}
                        >
                            Send Reset Link
                        </button>
                    </div>
                {/if}

                <div class="mt-4 flex items-center justify-center gap-2 text-sm text-white">
                    <button
                        class="text-pretty text-sm text-gray-500 hover:underline"
                        on:click={() => {
                            forgotPass = false;
                        }}
                    >
                        Back To Login
                    </button>
                </div>
            </div>
        {:else}
            <div class="w-full px-6 pb-8 md:px-8">
                <div class="mt-4">
                    <label
                        class="mb-2 block text-xs font-medium text-gray-600 dark:text-gray-200"
                        for="LoggingEmailAddress"
                    >
                        Email
                    </label>
                    <input
                        id="LoggingEmailAddress"
                        placeholder="Email"
                        class="block w-full rounded-lg border bg-white px-4 py-2 text-gray-700 focus:border-blue-400 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-40 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:focus:border-blue-300 {validationMap.has(
                            'email'
                        )
                            ? 'border-red-500'
                            : ''}"
                        type="email"
                        on:input={() => {
                            validationMap = new Map();
                        }}
                        bind:value={email}
                    />
                </div>

                <div class="mt-4">
                    <div class="flex items-center justify-between">
                        <label
                            class="mb-2 block text-xs font-medium text-gray-600 dark:text-gray-200"
                            for="loggingPassword"
                        >
                            Password
                        </label>
                        <button
                            class="text-xs text-gray-500 hover:underline dark:text-gray-300"
                            on:click={() => {
                                forgotPass = true;
                            }}
                        >
                            Forgot Password?
                        </button>
                    </div>
                    <div class="relative">
                        {#if showPassword}
                            <!-- Input field for visible password -->
                            <input
                                id="loggingPassword"
                                class="block w-full rounded-lg border bg-white px-4 py-2 text-gray-700 focus:border-blue-400 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-40 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:focus:border-blue-300
                            {validationMap.has('password') ? 'border-red-500' : ''}"
                                placeholder="Password"
                                on:input={() => {
                                    validationMap = new Map();
                                }}
                                type="text"
                                bind:value={password}
                            />
                        {:else}
                            <!-- Input field for hidden password -->
                            <input
                                on:keypress={({ key }) => {
                                    if (key === "Enter") {
                                        handleEmailLogin();
                                    }
                                }}
                                id="loggingPassword"
                                class="block w-full rounded-lg border bg-white px-4 py-2 text-gray-700 focus:border-blue-400 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-40 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:focus:border-blue-300
                            {validationMap.has('password') ? 'border-red-500' : ''}"
                                placeholder="Password"
                                on:input={() => {
                                    validationMap = new Map();
                                }}
                                type="password"
                                bind:value={password}
                            />
                        {/if}
                        <button
                            type="button"
                            on:click={togglePasswordVisibility}
                            class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 dark:text-gray-400"
                        >
                            {#if showPassword}
                                <!-- Eye Open Icon -->
                                <img src="/images/openEye.png" class="h-5" alt="show" />
                            {:else}
                                <!-- Eye Closed Icon -->
                                <img src="/images/closedEye.png" class="h-5" alt="show" />
                            {/if}
                        </button>
                    </div>
                </div>

                {#if isLoading}
                    <div class="mt-6">
                        <button
                            class="flex w-full transform items-center justify-center rounded-lg"
                        >
                            <Loader />
                        </button>
                    </div>
                {:else}
                    <div class="mt-6">
                        <button
                            class="w-full font-serif transform rounded-lg bg-gray-800 px-6 py-3 text-sm font-bold capitalize tracking-wide text-white transition-colors duration-300 hover:bg-gray-700 focus:outline-none focus:ring focus:ring-gray-300 focus:ring-opacity-50"
                            on:click={handleEmailLogin}
                        >
                            Sign In
                        </button>
                    </div>
                {/if}
            </div>
        {/if}
    </div>
</div>
