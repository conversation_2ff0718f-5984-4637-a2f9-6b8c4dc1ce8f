import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemAttribute, IItemAttributeValue } from "../models/IItemAttribute";


export interface IItemAttributesPresenter {
    getAllAttributes(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttribute>>>;
    getAllAttributeValues(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttributeValue>>>;
}