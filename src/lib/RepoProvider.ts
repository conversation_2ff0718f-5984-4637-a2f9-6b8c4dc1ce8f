import type { DocumentData } from "firebase/firestore";

import type { IUserRepo } from "$lib/users/repositories/IUserRepo";
import { UserRepo } from "./users/repositories/UserRepo";
import { DashboardRepo } from "./dashboard/repositories/DashboardRepo";
import type { IDashboardRepo } from "$lib/dashboard/repositories/IDashboardRepo";
import type { IOperationalLogsRepo } from "./operational-logs/repositories/IOperationalLogsRepo";
import { OperationalLogsRepo } from "./operational-logs/repositories/OperationalLogsRepo";
import type { ISupplierRepo } from "./supplier/repositories/ISupplierRepo";
import { SupplierRepo } from "./supplier/repositories/SupplierRepo";
import type { IStorageLocationRepo } from "./storage_locations/repositories/IStorageLocationRepo";
import { StorageLocationRepo } from "./storage_locations/repositories/StorageLocationRepo";
import type { IFactoryGatesRepo } from "./factory_gates/repositories/IFactoryGateRepo";
import { FactoryGatesRepo } from "./factory_gates/repositories/FactoryGateRepo";
import type { IItemCategoryRepo } from "./item_category/repositories/IItemCategoryRepo";
import { ItemCategoryRepo } from "./item_category/repositories/ItemCategoryRepo";
import type { IItemUnitRepo } from "./item_unit/repositories/IItemUnitRepo";
import type { IRawMaterialRepo } from "./raw_material/repositories/IRawMaterialRepo";
import { RawMaterialRepo } from "./raw_material/repositories/RawMaterialRepo";
import { ItemUnitRepo } from "./item_unit/repositories/ItemUnitRepo";
import type { IRawMaterialStockRepo } from "./raw_materia_stock/repositories/IRawMaterialStockRepo";
import { RawMaterialStockRepo } from "./raw_materia_stock/repositories/RawMaterialStockRepo";
import type { IPurchaseInvoiceRepo } from "./purchase_invoice/repositories/IPurchaseInvoiceRepo";
import { PurchaseInvoiceRepo } from "./purchase_invoice/repositories/PurchaseInvoiceRepo";
import type { IDebitNoteRepo } from "./debit-notes/repositories/IDebitNoteRepo";
import { DebitNoteRepo } from "./debit-notes/repositories/DebitNoteRepo";
import type { IUserRoleRepo } from "./user_role/repositories/IUserRoleRepo";
import { UserRoleRepo } from "./user_role/repositories/UserRoleRepo";
import type { IOpeningStockRepo } from "./opening-stock/repo/IOpeningStockRepo";
import { OpeningStockRepo } from "./opening-stock/repo/OpeningStockRepo";
import type { IAuthProvider } from "./auth/provider/IAuthProvider";
import { FirebaseAuthProvider } from "./auth/provider/FirebaseAuthProvider";
import type { IPurchaseOrder } from "./purchase-order/models/IPurchaseOrder";
import type { IPurchaseOrderRepo } from "./purchase-order/repositories/IPurchaseOrderRepo";
import { PurchaseOrderRepo } from "./purchase-order/repositories/PurchaseOrderRepo";
import type { IExcelUploadRepo } from "./excel_upload/repositories/IExcelUploadRepo";
import { ExcelUploadRepo } from "./excel_upload/repositories/ExcelUploadRepo";
import type { IItemAttributesRepo } from "./item_attributes/repositories/IItemAttributesRepo";
import { ItemAttributesRepo } from "./item_attributes/repositories/ItemAttributesRepo";


export class RepoProvider {
    private static _userRepo: IUserRepo;
    private static _dashboardRepo: IDashboardRepo;
    private static _operationalLogsRepo: IOperationalLogsRepo;
    private static _supplierRepo: ISupplierRepo;
    private static _storageLocationRepo: IStorageLocationRepo;
    private static _factoryGatesRepo: IFactoryGatesRepo;
    private static _itemCategoryRepo: IItemCategoryRepo;
    private static _itemUnitRepo: IItemUnitRepo;
    private static _rawMaterialRepo: IRawMaterialRepo;
    private static _rawMaterialStockRepo: IRawMaterialStockRepo;
    private static _purchaseInvoiceRepo: IPurchaseInvoiceRepo;
    private static _debitNoteRepo: IDebitNoteRepo;
    private static _userRoleRepo: IUserRoleRepo;
    private static _openingStockRepo: IOpeningStockRepo;
    private static _authProvider: IAuthProvider;
    private static _purchaseOrderRepo: IPurchaseOrderRepo;
    private static _excelUploadRepo: IExcelUploadRepo;
    private static _itemAttributesRepo: IItemAttributesRepo;

    static get purchaseOrderRepo(): IPurchaseOrderRepo {
        if (!this._purchaseOrderRepo) {
            this._purchaseOrderRepo = new PurchaseOrderRepo();
        }
        return this._purchaseOrderRepo;
    }


    static get authProvider(): IAuthProvider {
        if (!this._authProvider) {
            this._authProvider = new FirebaseAuthProvider();
        }
        return this._authProvider;
    }

    static get userRepo(): IUserRepo {
        if (!this._userRepo) {
            this._userRepo = new UserRepo();
        }
        return this._userRepo;
    }

    static get dashboardRepo(): IDashboardRepo {
        if (!this._dashboardRepo) {
            this._dashboardRepo = new DashboardRepo();
        }
        return this._dashboardRepo;
    }

    static get operationalLogsRepo(): IOperationalLogsRepo {
        if (!this._operationalLogsRepo) {
            this._operationalLogsRepo = new OperationalLogsRepo();
        }
        return this._operationalLogsRepo;
    }

    static get supplierRepo(): ISupplierRepo {
        if (!this._supplierRepo) this._supplierRepo = new SupplierRepo();
        return this._supplierRepo;
    }

    static get storageLocationRepo(): IStorageLocationRepo {
        if (!this._storageLocationRepo) this._storageLocationRepo = new StorageLocationRepo();
        return this._storageLocationRepo;
    }

    static get factoryGatesRepo(): IFactoryGatesRepo {
        if (!this._factoryGatesRepo) this._factoryGatesRepo = new FactoryGatesRepo();
        return this._factoryGatesRepo;
    }

    static get itemCategoryRepo(): IItemCategoryRepo {
        if (!this._itemCategoryRepo) this._itemCategoryRepo = new ItemCategoryRepo();
        return this._itemCategoryRepo;
    }

    static get itemUnitRepo(): IItemUnitRepo {
        if (!this._itemUnitRepo) this._itemUnitRepo = new ItemUnitRepo();
        return this._itemUnitRepo;
    }

    static get rawMaterialRepo(): IRawMaterialRepo {
        if (!this._rawMaterialRepo) this._rawMaterialRepo = new RawMaterialRepo();
        return this._rawMaterialRepo;
    }

    static get rawMaterialStockRepo(): IRawMaterialStockRepo {
        if (!this._rawMaterialStockRepo) this._rawMaterialStockRepo = new RawMaterialStockRepo();
        return this._rawMaterialStockRepo;
    }

    static get purchaseInvoiceRepo(): IPurchaseInvoiceRepo {
        if (!this._purchaseInvoiceRepo) this._purchaseInvoiceRepo = new PurchaseInvoiceRepo();
        return this._purchaseInvoiceRepo;
    }

    static get debitNoteRepo(): IDebitNoteRepo {
        if (!this._debitNoteRepo) this._debitNoteRepo = new DebitNoteRepo();
        return this._debitNoteRepo;
    }

    static get userRoleRepo(): IUserRoleRepo {
        if (!this._userRoleRepo) this._userRoleRepo = new UserRoleRepo();
        return this._userRoleRepo;
    }

    static get openingStockRepo(): IOpeningStockRepo {
        if (!this._openingStockRepo) this._openingStockRepo = new OpeningStockRepo();
        return this._openingStockRepo;
    }

    static get excelUploadRepo(): IExcelUploadRepo {
        if (!this._excelUploadRepo) this._excelUploadRepo = new ExcelUploadRepo();
        return this._excelUploadRepo;
    }
    static get itemAttributesRepo(): IItemAttributesRepo {
        if (!this._itemAttributesRepo) this._itemAttributesRepo = new ItemAttributesRepo();
        return this._itemAttributesRepo;
    }
}
