import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IRawMaterialStockPresenter } from "./IRawMaterialStockPresenter";
import type { IReceiveRawMaterialStock, IRawMaterialStock, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStock, IRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStock";
import { RawMaterialStockUtils } from "../utils/RawMaterialStockUtils";
import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
import type { IUser } from "$lib/users/models/User";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class RawMaterialStockPresenter implements IRawMaterialStockPresenter {
    getStockIssuanceByEntryId(id: string): Promise<DTO<IRawMaterialStockIssuanceResponse>> {
       return RepoProvider.rawMaterialStockRepo.getStockIssuanceByEntryId(id);
    }




    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>> {
        return RepoProvider.rawMaterialStockRepo.getAll(page, pageSize);
    }



    searchByRawMaterial(rawMaterialName: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>> {
        return RepoProvider.rawMaterialStockRepo.searchByRawMaterial(rawMaterialName);
    }

    onSubmit(payload: IReceiveRawMaterialStock): Promise<DTO<null>> {
        return RepoProvider.rawMaterialStockRepo.receive(payload);
    }

    onValidate(payload: IReceiveRawMaterialStock): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = RawMaterialStockUtils.creationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }


    validateUpdate(payload: IRawMaterialStockUpdateRequest): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = RawMaterialStockUtils.updateSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }

    validateAssignStorageLocation(payload: IAssignStorageToStock): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = RawMaterialStockUtils.assignStorageLocationSchema.safeParse(payload);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }



    getSuppliers(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<ISupplier>>> {
        return RepoProvider.supplierRepo.getAll(page, pageSize);
    }
    getStorageLocations(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        return RepoProvider.storageLocationRepo.getAll(page, pageSize);
    }
    getFactoryGates(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>> {
        return RepoProvider.factoryGatesRepo.getAll(page, pageSize);
    }
    getRawMaterialDetails(id: number): Promise<DTO<IRawMaterial>> {
        return RepoProvider.rawMaterialRepo.getById(id);
    }

    getUsers(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IUser>>> {
        return RepoProvider.userRepo.getAll(page, pageSize);
    }


    getStockInEntries(page: number, pageSize: number,text?:string,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.stockInEntries(page, pageSize,text,startDate,endDate);
    }

    searchStockInByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.searchStockInByText(text);
    }

    searchStockInByTextWithoutStorage(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.searchStockInByTextWithoutStorage(text);
    }

    getStockInEntriesByDate(
        startDate: string,
        endDate: string,
        page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.stockInEntriesByDate(
            startDate,
            endDate,
            page, pageSize);
    }

    update(payload: IRawMaterialStockUpdateRequest): Promise<DTO<null>> {
        return RepoProvider.rawMaterialStockRepo.update(payload);
    }

    getById(id: number): Promise<DTO<IRawMaterialStock>> {
        return RepoProvider.rawMaterialStockRepo.getById(id);
    }
    getStockInEntriesWithoutStorageLocation(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.getStockInEntriesWithoutStorageLocation(page, pageSize);
    }
    getStockInEntryById(id: number): Promise<DTO<IRawMaterialStockInDetails>> {
        return RepoProvider.rawMaterialStockRepo.getStockInEntryById(id);
    }
    assignStorageLocation(payload: IAssignStorageToStock): Promise<DTO<null>> {
        return RepoProvider.rawMaterialStockRepo.assignStorageLocation(payload);
    }

    
    validateStockIssuance(payload: IRawMaterialStockIssuance): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = RawMaterialStockUtils.stockIssuanceSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }

    issueStock(payload: IRawMaterialStockIssuance): Promise<DTO<null>> {
        return RepoProvider.rawMaterialStockRepo.issueStock(payload);
    }

    getAllIssuedStock(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>> {
        return RepoProvider.rawMaterialStockRepo.getAllIssuedStock(page, pageSize);
    }

    searchIssuedByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>> {
        return RepoProvider.rawMaterialStockRepo.searchIssuedByText(text);
    }

    getByRawMaterialId(rawMaterialId: number): Promise<DTO<IRawMaterialStock>> {
        return RepoProvider.rawMaterialStockRepo.getByRawMaterialId(rawMaterialId);
    }

    exportCurrentStock(categoryId:number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        return RepoProvider.rawMaterialStockRepo.exportCurrentStock(categoryId);
    }
}