import type { IRawMaterialOld } from "$lib/raw_material/models/IRawMaterial";
import type { ISupplier } from "$lib/supplier/models/ISupplier";

export interface IPurchaseOrderItems {
    id: number;
    qty: number;
    name: string;
}

export interface PurchaseItemDetails{
  item:IRawMaterialOld,
  qty:number;
}

export interface IPurchaseOrder 
{
    id:number,
    poNumber:string,
    supplier: ISupplier,
    expectedDate:Date,
    items: PurchaseItemDetails[]
    
}
export interface IPurchaseOrderPayload extends IPurchaseOrder {
    orderItems:IPurchaseOrderItems[];
}

export interface ICreatePurchaseOrderPayload
{
  poNumber:string,
  supplierId:number,
  expectedDate:Date,
  items: IPurchaseOrderItems[],

}

export interface IUpdatePurchaseOrderPayload extends ICreatePurchaseOrderPayload
{
  id:number,
}
