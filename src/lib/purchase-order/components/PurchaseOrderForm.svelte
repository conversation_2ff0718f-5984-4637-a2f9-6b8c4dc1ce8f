<script lang="ts">
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { CloseButton, Input, Label } from "flowbite-svelte";
    import type {
        IPurchaseOrder,
        IPurchaseOrderItems,
        PurchaseItemDetails,
    } from "../models/IPurchaseOrder";
    import { getEmptyPurchaseOrderItems } from "../utils/purchase-order-utils";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import SupplierRawMaterialSearch from "$lib/purchase_invoice/components/SupplierRawMaterialSearch.svelte";
    import {
        formatDateToInput,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { RAW_MATERIAL_STAUS, type IRawMaterialOld } from "$lib/raw_material/models/IRawMaterial";
    import { RepoProvider } from "$lib/RepoProvider";
    import { goto } from "$app/navigation";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import { map } from "zod";

    export let isEditCase: boolean = false;
    export let formData: IPurchaseOrder = getEmptyPurchaseOrderItems();
   
    let selectedSupplier: ISupplier | null = null;
    let selectedItemsMap: Map<string, IPurchaseOrderItems> = new Map();
    let isLoading: boolean = true;
    let oldRawMaterialsIds: number[] = [];
    let validationErrors: Map<string, string> = new Map();

    const handleTotalQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                formData.items[index].qty = 0;
                return;
            }
            formData.items[index].qty = parseFloat(e.target.value);
            console.log(formData.items[index].qty);
            let price = 1;
            let data = selectedItemsMap.get(index.toString());
        } catch (error) {
            formData.items[index].qty = 0;
        }
    };
    const onRawMaterialSelected = (
        data: IRawMaterialOld,
        item: PurchaseItemDetails,
        index: number
    ) => {
        if (data) {
            if (oldRawMaterialsIds.includes(data.id)) {
                showErrorToast(data.name + " is already added");
                return;
            }

            const gstPercentage = data.gstPercentage;

            item.item = data;
            selectedItemsMap.set(index.toString(), {
                id: data.id,
                qty: 0,
                name: data.name,
            });
        } else {
            oldRawMaterialsIds = oldRawMaterialsIds.filter((id) => id !== item.item.id);

            selectedItemsMap.delete(index.toString());
            formData.items.splice(index, 1);
            // Recreate selected item map
            selectedItemsMap = new Map();
            formData.items.forEach((item, idx) => {
                selectedItemsMap.set(idx.toString(), {
                    id: item.item.id,
                    qty: item.qty,
                    name: item.item.name,
                });
            });
        }

        formData = formData;
        selectedItemsMap = selectedItemsMap;
        console.log(selectedItemsMap, "selecteditem map");
        console.log(formData, "formData");
    };
    const getSelected = (index: number) => {
        console.log(index, "iii");
        console.log(selectedItemsMap, "sss");
        const item = selectedItemsMap.get(index.toString());
        console.log(item);
        return item
            ? ({
                  id: item.id,
                  qty: item.qty,
                  name: item.name,
              } as unknown as IRawMaterialOld)
            : null;
    };

    const onSubmitHandler = async () => {
        isLoading = true;
        validationErrors = PresenterProvider.purchaseOrderPresenter.onValidate(formData);
        if (validationErrors.size !== 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            isLoading = false;
            return;
        }
        if (!isEditCase) {
            const res = await RepoProvider.purchaseOrderRepo.create(formData);
            if (res.success) {
                showSuccessToast("Purchase Order Saved Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        } else {
            const res = await RepoProvider.purchaseOrderRepo.update(formData);
            if (res.success) {
                showSuccessToast("Purchase Order Updated Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        }
        isLoading = false;
    };

    $: {
        selectedItemsMap;
    }

    onMount(() => {
        console.log(formData.supplier);
        if (formData.supplier.id > 0) {
            selectedSupplier = formData.supplier;
            if (formData.id) {
                formData.id = formData.id;
                if (formData.id > 0) {
                    formData.expectedDate = new Date(formData.expectedDate);
                }
            }
        }
        let index = 0;
        for (const item of formData.items) {
            oldRawMaterialsIds.push(item.item.id);

            selectedItemsMap.set(index.toString(), {
                id: item.item.id,
                qty: item.qty,
                name: item.item.name,
            });

            index++;
        }
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class="w-[90vw] p-2">
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={"Purchase Order Item"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <hr class="mb-5" />
            <div class=" grid grid-cols-3 gap-4 mt-5">
                <div>
                    <Label for="purchaseOrderNo" class="mb-2 font-sans capitalize tracking-[0px]">
                        Purchase Order No.
                    </Label>
                    <Input
                        type="text"
                        id="poNumber"
                        placeholder="PO No."
                        class="uppercase dark:bg-primary-700"
                        bind:value={formData.poNumber}
                        on:input={() => {
                            validationErrors = new Map();
                        }}
                    />
                    {#if validationErrors.has("poNumber")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("poNumber")}
                        </p>
                    {/if}
                </div>
                <div>
                    <SupplierSearch
                        selected={selectedSupplier}
                        onSelected={(data) => {
                            if (data) {
                                formData = { ...formData, id: data.id };
                                formData.supplier.id = data.id;
                                formData.supplier.name = data.name;
                                formData.items = [
                                    {
                                       item:{
                                          id: -1,
                                          name: "",
                                          gstPercentage: 0,
                                          price: 0,
                                          unitId: -1,
                                          unitName: "",
                                          categoryId: -1,
                                          categoryName: "",
                                          hsn: "",
                                          msq: 0,
                                          sku: "",
                                          status: RAW_MATERIAL_STAUS.ACTIVE,
                                          priceData: [],
                                          createdAt: new Date(),
                                          updatedAt: null,
                                          deletedAt: null,
                                          createdBy: '',
                                          updatedBy: null,
                                          deletedBy: null,
                                       },
                                        qty: 0,
                                    },
                                ];
                                name;
                            } else {
                                formData.supplier.id = -1;
                                formData.items = [];
                            }
                            selectedSupplier = data;
                            validationErrors = new Map();
                        }}
                    />
                    {#if validationErrors.has("supplier")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplier")}
                        </p>
                    {/if}
                </div>
                <div>
                    <Label for="expectedData" class="mb-2 font-sans capitalize tracking-[0px]">
                        Expected Date
                    </Label>
                    <Input
                        type="date"
                        id="expectedData"
                        placeholder="Expected Data"
                        class="dark:bg-primary-700"
                        value={formatDateToInput(formData.expectedDate)}
                        onchange={(e: any) => {
                            formData.expectedDate = new Date(e.target.value);
                        }}
                    />
                    {#if validationErrors.has("expectedDate")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("expectedDate")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>
            <span class="text-sm italic">Items</span>
            <div class="m-2"></div>
            <div class="overflow-x-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Total Qty
                            </th>
                            <th scope="col" class="px-6 py-3">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each formData.items as item, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    {index + 1}
                                </td>
                                <td class="px-6 py-4 w-[300px] h-[60px]">
                                    <div class="mb-[35px] w-[250px]">
                                        <SupplierRawMaterialSearch
                                            supplierId={formData.supplier.id}
                                            selected={getSelected(index)}
                                            onSelected={(data) => {
                                                onRawMaterialSelected(data, item, index);
                                            }}
                                        />
                                        {#if validationErrors.has("expectedDate")}
                                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                            {validationErrors.get("expectedDate")}
                                        </p>
                                    {/if}
                                    </div>
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.qty}
                                        on:change={(e) => {
                                            handleTotalQty(e, index);
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <CloseButton
                                        class="w-content mb-[35px]"
                                        on:click={() => {
                                            const deletedItemId = formData.items[index].item.id;
                                    
                                            // Remove the item from the array (using slice for reactivity)
                                            formData.items = formData.items.filter((item) => item.item.id !== deletedItemId);
                                    
                                            // Update selectedItemsMap based on IDs instead of indexes
                                            selectedItemsMap.delete(String(deletedItemId));
                                    
                                            // Update oldRawMaterialsIds correctly
                                            oldRawMaterialsIds = oldRawMaterialsIds.filter((id) => id !== deletedItemId);
                                        }}
                                    />
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>

            <div class="mt-5 flex w-full justify-between">
                <div>
                    {#if formData.items.length > 0}
                        <CustomButton
                            onClick={() => {
                                formData.items.push({
                                    item:{
                                          id: -1,
                                          name: "",
                                          gstPercentage: 0,
                                          price: 0,
                                          unitId: -1,
                                          unitName: "",
                                          categoryId: -1,
                                          categoryName: "",
                                          hsn: "",
                                          msq: 0,
                                          sku: "",
                                          status: RAW_MATERIAL_STAUS.ACTIVE,
                                          priceData: [],
                                          createdAt: new Date(),
                                          updatedAt: null,
                                          deletedAt: null,
                                          createdBy: '',
                                          updatedBy: null,
                                          deletedBy: null,
                                       },
                                        qty: 0,
                                });
                                formData = formData;
                            }}
                            cssClass=" bg-black"
                            title={"Add new item"}
                        />
                    {/if}
                </div>
                <CustomButton onClick={onSubmitHandler} cssClass="w-32 bg-black" title={"Save"} />
            </div>
        </div>
    </div>
{/if}
