const SERVER_URL = "http://127.0.0.1:3002";
// const SERVER_URL = "https://gctacademy.com";
const API = SERVER_URL + "/api";
const ADMIN = SERVER_URL + "/api/admin";
const GUEST = SERVER_URL + "/api/guest";
const FRANCHISE = SERVER_URL + "/franchise";
const CATEGORY = ADMIN + "/products-category";
const OPERATION_LOGS = API + "/logs";
const FRONTEND = SERVER_URL + "/";
const LEAD = ADMIN + "/lead";
const ZOHO_TEMPLATES_API = API + "/zoho-sign";
const CONTRACTS = ADMIN + "/contracts";
const USERS = ADMIN + "/users";
const QUESTIONS = ADMIN + "/question";
const TAXES = ADMIN + "/tax";
const PROPOSAL = LEAD + "/Proposal-model";
const FRANCHISEE_MANAGEMENT = ADMIN + "/franchise";
const CAMPAIGNS = ADMIN + "/campaign-ad";
const RETORT = ADMIN + "/retort";
const RETORT_PRODUCTS = RETORT + "/product";
const RETORT_CATEGORY = RETORT + "/category";
const VENDORS = ADMIN + "/vendors";
const FILES_UPLOAD = ADMIN + "/files";
const GALLERY = ADMIN + "/gallery";
const ECOMMERCE_PRODUCTS = ADMIN + "/product";
const ECOMMERCE_CATEGORY = ECOMMERCE_PRODUCTS + "/category";
const ORDERS = ADMIN + "/order";
const SHIPPING_HISTORY = ADMIN + "/shipping-history";
const AUTH = API + "/auth";
const CART = API + "/cart";
const ORGANIZATION_API = API + "/organization";
const ORDER_PAYMENT = API + "/order-payment";
const FRANCHISE_MODELS = LEAD + "/franchise-model";
const AREA = ADMIN + "/area";
const REGION = ADMIN + "/region"
const AFFILIATE = ADMIN + "/lead/affiliate"
const CHECKPOINT = ADMIN + "/checkpoint"
const CHECKLIST = ADMIN + "/checklist"
const PDI_API = ADMIN + "/pdi"
const COMMISSION_API = ADMIN + "/commission"
const PRODUCT_API = ADMIN + "/product"
const TRANSACTION_API = API + "/transaction"
const SUPPLIER_API_PATH = API + "/suppliers"
const VENDOR_API_PATH = API + "/vendors"
const STORAGE_LOCATION_API_PATH = API + "/storage-locations"
const FACTORY_GATES_API_PATH = API + "/factory-gates"
const ITEM_CATEGORIES_API_PATH = API + "/item-categories"
const ITEM_UNITS_API_PATH = API + "/item-units"
const RAW_MATERIAL_API_PATH = API + "/raw-materials"
const RAW_MATERIAL_STOCK_API_PATH = API + "/raw-materials-stock"
const PURCHASE_INVOICE_API_PATH = API + "/purchase-invoices"
const PURCHASE_ORDER_API_PATH = API + "/purchase-orders"
const USI_USERS_API_PATH = API + "/users"
const USER_ROLES_API_PATH = API + "/user-roles"
const DEBIT_NOTE_API_PATH = API + "/debit-notes"
const DEBIT_NOTE_API_PATH_MANUAL = API + "/debit-notes/manual"

const LOGS_API_PATH = API + "/logs"
const OPENING_STOCK_API_PATH = API + "/opening-stocks"
const EXCEL_API_PATH = API + "/excel-processor";
const ITEM_ATTRIBUTES_API_PATH = API + "/item-attributes";


export {
    SERVER_URL,
    API,
    ADMIN,
    GUEST,
    FRANCHISE,
    CATEGORY,
    OPERATION_LOGS,
    FRONTEND,
    LEAD,
    ZOHO_TEMPLATES_API,
    CONTRACTS,
    USERS,
    QUESTIONS,
    CAMPAIGNS,
    PROPOSAL,
    PRODUCT_API,
    FRANCHISEE_MANAGEMENT,
    FILES_UPLOAD,
    GALLERY,
    ECOMMERCE_PRODUCTS,
    ECOMMERCE_CATEGORY,
    RETORT,
    RETORT_PRODUCTS,
    RETORT_CATEGORY,
    VENDORS,
    ORDERS,
    AUTH,
    CART,
    ORDER_PAYMENT,
    FRANCHISE_MODELS,
    SHIPPING_HISTORY,
    AREA,
    REGION,
    ORGANIZATION_API,
    AFFILIATE,
    CHECKPOINT,
    CHECKLIST,
    PDI_API,
    COMMISSION_API,
    TRANSACTION_API,
    SUPPLIER_API_PATH,
    VENDOR_API_PATH,
    STORAGE_LOCATION_API_PATH,
    FACTORY_GATES_API_PATH,
    ITEM_CATEGORIES_API_PATH,
    ITEM_UNITS_API_PATH,
    RAW_MATERIAL_API_PATH,
    RAW_MATERIAL_STOCK_API_PATH,
    PURCHASE_INVOICE_API_PATH,
    USI_USERS_API_PATH,
    DEBIT_NOTE_API_PATH,
    LOGS_API_PATH,
    USER_ROLES_API_PATH,
    OPENING_STOCK_API_PATH,
    DEBIT_NOTE_API_PATH_MANUAL,
    PURCHASE_ORDER_API_PATH,
    EXCEL_API_PATH,
    ITEM_ATTRIBUTES_API_PATH,
};
