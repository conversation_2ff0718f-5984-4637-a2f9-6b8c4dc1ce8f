<script lang="ts">
    import { isEmptyString } from "$lib/common/utils/validation";
    import { Label, Input } from "flowbite-svelte";
    import { onMount } from "svelte";
    export let isLabel: boolean = true;
    export let label: string = "";
    export let searchTerm: string = "";
    export let searchInput: (event: string) => void = (e: string) => {};
    export let onchange: (event: string) => void = (e: string) => {};
    export let selected: Array<any> = [];
    export let filteredData: Array<any> | null = ["w"];
    export let level = "";
    export let selectedFunc: (selected: any) => void = () => {};
    export let loading = false;
    export let errorMap = new Map();
    export let fieldName: string = "";
    export let removeInput: (selected: any) => void = (selected) => {};
    export let isEditCase: boolean = false;

    let componentRef: HTMLDivElement | undefined;
    let showSuggestions = false;

    // Update showSuggestions based on searchTerm and filteredData
    $: showSuggestions = searchTerm.length > 0 && Boolean(filteredData && filteredData.length >= 0);

    // <PERSON>le click outside to close suggestions
    function handleClickOutside(event: MouseEvent) {
        if (componentRef && !componentRef.contains(event.target as Node)) {
            showSuggestions = false;
            searchTerm = "";
        }
    }

    onMount(() => {
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    });
    function removeIn(i: number): any {
        selected.splice(i, 1);
        selected = selected;
        console.log(selected)
        removeInput(selected);
    }
    // const sortAlphabetically = () => {
    //     if (filteredData && !isEmptyString(level)) {
    //         filteredData.sort((a, b) => a[`${level}`].localeCompare(b[`${level}`]));
    //     }else if(filteredData){
    //         filteredData.sort((a, b) => a.localeCompare(b));
    //     }
    // };
    console.log(filteredData)
    // onMount(()=>{
    //     sortAlphabetically();
    // })

    $: {
        // sortAlphabetically();
        selected = selected;
    }
</script>

<div bind:this={componentRef}>
    {#if isLabel}
        <Label for={label} class="mb-2  text-sm capitalize tracking-[0px]">
            {label}
            {#if errorMap}
                <span class="{errorMap.has(fieldName)?'text-red-500':'text-black'}">*</span>
            {/if}
        </Label>
    {/if}
    <div class="relative text-sm">
        <div class="flex gap-2">
            <Input
                type="text"
                placeholder="Search {label}..."
                bind:value={searchTerm}
                class="border  p-2 font-poppins dark:bg-primary-700 {errorMap &&
                errorMap.has(fieldName)
                    ? 'border-red-500'
                    : ''}"
                on:input={(e) => {
                    // @ts-ignore
                    searchInput(e.currentTarget.value);
                    showSuggestions = true;
                }}
                on:change={(e) => {
                    // @ts-ignore
                    onchange(e.currentTarget.value);
                }}
                on:focus={() => {
                    if (searchTerm.length > 0) {
                        showSuggestions = true;
                    }
                }}
            />
            <button class="absolute right-0 top-0 p-4" on:click={()=>{
                    if(searchTerm===""){
                        searchTerm=" ";
                        showSuggestions = true;
                    }else{
                        searchTerm="";
                        showSuggestions = false;
                    }
                }}>
                <img src="/images/dropdown.png" class="w-[15px]" alt="drop"/>
            </button>
        </div>

        {#if showSuggestions && filteredData && filteredData.length > 0 && !loading}
            <ul
                class="absolute z-10 mt-1 max-h-52 w-full overflow-y-auto rounded border border-gray-300 bg-white shadow-lg"
            >
                {#each filteredData as data}
                    <!-- svelte-ignore a11y-click-events-have-key-events -->
                    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
                    <li
                        on:click={() => {
                            selectedFunc(data);
                            searchTerm = "";
                            showSuggestions = false;
                            selected = Array.from(new Set(selected));
                        }}
                        class="cursor-pointer p-2 text-sm transition-colors duration-200 hover:bg-gray-100"
                    >
                         {isEmptyString(level) ? data : data[`${level}`]}
                    </li>
                {/each}
            </ul>
        {:else if showSuggestions && searchTerm.length > 0 && !loading}
            <ul
                class="absolute z-10 mt-1 max-h-52 w-full overflow-y-auto rounded border border-gray-300 bg-white shadow-lg"
            >
                <li class="cursor-pointer p-2 hover:bg-gray-100">
                    No {label} Found
                </li>
            </ul>
        {/if}
    </div>
    <div class="m-1 flex flex-wrap gap-2">
        {#each selected as inputVal, i}
            <span
                id="badge-dismiss-pink-{inputVal}"
                class="mt-1 ml-0 inline-flex items-center rounded bg-gray-300 font-poppins px-2 py-1 text-sm font-medium text-black dark:bg-pink-900 dark:text-pink-300"
            >
                {isEmptyString(level) ? inputVal : inputVal[`${level}`]}

                {#if !isEditCase}
                    <button
                        on:click={() => removeIn(i)}
                        type="button"
                        class="ms-2 inline-flex items-center rounded-sm bg-transparent p-1 text-sm text-pink-400 hover:bg-pink-200 hover:text-pink-900 dark:hover:bg-pink-800 dark:hover:text-pink-300"
                        data-dismiss-target="#badge-dismiss-pink-{inputVal}"
                        aria-label="Remove"
                    >
                    <img src="/images/close.svg" alt="close" class="w-4 ml-1"/>

                    </button>
                {/if}
            </span>
        {/each}
    </div>
</div>
