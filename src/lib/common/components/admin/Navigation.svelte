<script lang="ts">
    import { loggedInUser } from "$lib/common/utils/store";
    import { NavBrand, NavHamburger } from "flowbite-svelte";
    import NavContainer from "flowbite-svelte/NavContainer.svelte";
    import CustomButton from "./CustomButton.svelte";
    import { signOut } from "firebase/auth";
    import { usiFirebaseAuth } from "../../../../firebaseInit";

    export let toggleDrawer: () => void = () => {};

    // $: {
    //     loggedInUser;
    //     console.log($loggedInUser);
    // }
</script>

<header class="fixed left-0 right-0 top-0 z-[10] !mx-0">
    <NavContainer class="!mx-0 h-[5rem] !max-w-[100vw] !items-center !bg-primary">
        <NavBrand class="!mx-0 w-[80%]">
            <!--Hamburger-->
            <button on:click={toggleDrawer} class="mx-[2%] text-white">
                <NavHamburger class="!block   p-1" />
            </button>
            <!-- <Cog /> -->

            <div class=" flex w-full items-center justify-center">
                <button
                    on:click={async () => {}}
                    class=" flex w-[65%] items-center justify-end whitespace-nowrap pl-[10%] text-xl font-semibold"
                >
                    <img
                        src="/images/logo-white.png"
                        class="mr-1 h-[3.5rem] object-contain"
                        alt="Logo"
                    />
                </button>
                <div class="flex h-full w-[35%] items-center justify-end">
                    <!-- <a
                        href="/admin/rm-order"
                        class=" rounded-lg bg-gray-950 px-3 py-2 !font-primary text-white"
                    >
                        RM-Ordering System
                    </a> -->
                </div>
            </div>
        </NavBrand>

        <div class="flex w-[20%] items-center justify-end pr-[3%]">
            <span class="m-5 block truncate text-[16px] text-white">
                Hi, {$loggedInUser?.firstName}
            </span>

            <CustomButton
                title="Logout"
                onClick={async () => {
                    await signOut(usiFirebaseAuth);
                    loggedInUser.update(() => null);
                }}
            />
            <!-- <Avatar id="avatar-menu" src={avatarImg} />
            <button on:click={() => showModal(MODAL_TYPE.LOGOUT)} class="flex items-center">
                <svg
                    class="pl-2"
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    fill="#000000"
                    viewBox="0 0 256 256"
                >
                    <path
                        d="M120,216a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V40a8,8,0,0,1,8-8h64a8,8,0,0,1,0,16H56V208h56A8,8,0,0,1,120,216Zm109.66-93.66-40-40a8,8,0,0,0-11.32,11.32L204.69,120H112a8,8,0,0,0,0,16h92.69l-26.35,26.34a8,8,0,0,0,11.32,11.32l40-40A8,8,0,0,0,229.66,122.34Z"
                    ></path>
                </svg>
            </button> -->
        </div>
    </NavContainer>
    <!-- <div
        class="h-[15px] w-full bg-[url('/images/navigation/pattern.jpeg')] bg-contain bg-repeat-x"
    ></div> -->
</header>
