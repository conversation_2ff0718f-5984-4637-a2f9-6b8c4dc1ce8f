<script lang="ts">
    import {
        Drawer,
        Sidebar,
        SidebarGroup,
        SidebarItem,
        SidebarWrapper,
        Label,
    } from "flowbite-svelte";
    import { circInOut, quintOut } from "svelte/easing";
    import { onMount } from "svelte";
    import { isUserLoggedIn, sidebarOpen, userPermissions } from "$lib/common/utils/store";
    import { page } from "$app/stores";
    import { slide } from "svelte/transition";
    import { browser } from "$app/environment";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    export let drawerHidden: boolean = false;
    export let width: number;

    let transitionParams = {
        x: -320,
        duration: 400,
        easing: circInOut,
    };
    let breakPoint: number = 1280;
    let backdrop: boolean = false;
    let activateClickOutside = true;

    $: {
        if (browser) {
            drawerHidden = $sidebarOpen;
        }
    }

    onMount(() => {
        if (width >= breakPoint) {
            drawerHidden = false;
            activateClickOutside = false;
        } else {
            drawerHidden = true;
            activateClickOutside = true;
        }
    });
    const toggleSide = () => {
        if (width < breakPoint) {
            drawerHidden = !drawerHidden;
        }
    };
    let navigationSpanNormalClass = "ml-3 font-primary";
    let navigationSpanActiveClass = "!text-black   ml-3 font-primary";
    let navigationButtonClass = "smooth group relative w-full";

    let activeUrl = "";

    $: {
        activeUrl = $page.url.pathname;
        console.log(activeUrl, "**");
    }

    // $: $isUserLoggedIn;
</script>

<Drawer
    transitionType="fly"
    {backdrop}
    {transitionParams}
    bind:hidden={drawerHidden}
    bind:activateClickOutside
    width="w-64"
    class="hideScroll top-[60px] z-[1] overflow-y-scroll bg-primary-500 pb-32 pt-0 duration-500 ease-in-out "
    id="sidebar"
    style="scrollbar-width: none;"
>
    <Sidebar asideClass="min-w-[120px]" {activeUrl}>
        <SidebarWrapper divClass="overflow-y-auto py-4 pl-2 rounded   mt-7">
            <SidebarGroup ulClass="space-y-5 ">
                <Label class="mb-0 mt-5 font-primary  text-xl tracking-widest text-white">
                    Dashboard
                </Label>
                <hr class="!my-0 border border-white py-0" />

                <div class="space-y-4">
                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/user-roles')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/user-roles")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/user-roles"}
                            label="User Roles"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/suppliers.png"
                                    alt="Suppliers"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/user-roles/add"
                            class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/users')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/users")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/users"}
                            label="Users"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/suppliers.png"
                                    alt="Suppliers"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/users/add"
                            class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/suppliers')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/suppliers")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/suppliers"}
                            label="Suppliers"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/suppliers.png"
                                    alt="Suppliers"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/suppliers/add"
                            class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/storage-locations'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/storage-locations")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/storage-locations"}
                            label="Storage Locations"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/storage-locations.png"
                                    alt="Storage Locations"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/storage-locations/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/factory-gates'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/factory-gates")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/factory-gates"}
                            label="Factory Gates"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/factory-gates.png"
                                    alt="Factory Gates"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/factory-gates/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/item-categories'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/item-categories")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/item-categories"}
                            label="Item Categories"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/item-categories.png"
                                    alt="Item Categories"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/item-categories/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>
                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/item-units')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/item-units")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/item-units"}
                            label="Item Units"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/item-units.png"
                                    alt="Item Units"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/item-units/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl === '/admin/raw-materials' ||
                        activeUrl === '/admin/raw-materials/add'
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl === "/admin/raw-materials" ||
                            activeUrl === "/admin/raw-materials/add"
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/raw-materials"}
                            label="Raw Material"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/raw-material.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/raw-materials/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/purchase-invoices'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl === "/admin/purchase-invoices" ||
                            activeUrl.includes("/admin/purchase-invoices")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/purchase-invoices"}
                            label="Purchase Invoices"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/purchase-invoices/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/purchase-orders'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl === "/admin/purchase-orders" ||
                            activeUrl.includes("/admin/purchase-orders")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/purchase-orders"}
                            label="Purchase Orders"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/purchase-orders/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>
                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl ===
                        '/admin/raw-materials-stock'
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl === "/admin/raw-materials-stock"
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/raw-materials-stock"}
                            label="Current Stock"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar/crm management.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <!-- <a
                            href="/admin/raw-materials-stock/receive"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a> -->
                    </button>
                    <button
                    transition:slide={{
                        delay: 250,
                        duration: 450,
                        easing: quintOut,
                        axis: "y",
                    }}
                    class="smooth group relative w-full {activeUrl ===
                    '/admin/raw-materials-stock'
                        ? '!bg-gray-200 !rounded-md'
                        : ''}"
                >
                    <SidebarItem
                        spanClass={activeUrl === "/admin/opening-stock"
                            ? navigationSpanActiveClass
                            : navigationSpanNormalClass}
                        href={"/admin/opening-stock"}
                        label="Opening Stock"
                        on:click={toggleSide}
                    >
                        <svelte:fragment slot="icon">
                            <img
                                src="/images/sidebar/crm management.png"
                                alt="Raw Material"
                                class="h-5"
                            />
                        </svelte:fragment>
                    </SidebarItem>
                    <!-- <a
                        href="/admin/raw-materials-stock/receive"
                        class="absolute right-2 top-2 hidden group-hover:block"
                    >
                        <img src="/images/sidebar/add.svg" alt="add" />
                    </a> -->
                </button>
                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl ===
                        '/admin/raw-materials-stockin'
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl === "/admin/raw-materials-stock/in"
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/raw-materials-stock/in"}
                            label="Stock In Details"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar/crm management.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <!-- <a
                            href="/admin/raw-materials-stock/receive"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a> -->
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/debit-notes'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/debit-notes")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/debit-notes"}
                            label="Debit notes"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/assign-storage-to-raw-material'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/assign-storage-to-raw-material")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/assign-storage-to-raw-material"}
                            label="Assign storage"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/raw-materials-stock/issuance'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/raw-materials-stock/issuance")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/raw-materials-stock/issuance"}
                            label="Stock Issuance"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/raw-materials-stock/issuance/issue"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes(
                            '/admin/stock-adjustments'
                        )
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/stock-adjustments")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/stock-adjustments"}
                            label="Stock Adjustments"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/item-categories.png"
                                    alt="Item Categories"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/stock-adjustments/add"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>
                    <!-- logs -->
                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/logs')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/logs")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/logs"}
                            label="Logs"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Raw Material"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                    </button>

                    <button
                        transition:slide={{
                            delay: 250,
                            duration: 450,
                            easing: quintOut,
                            axis: "y",
                        }}
                        class="smooth group relative w-full {activeUrl.includes('/admin/excel-upload/upload')
                            ? '!bg-gray-200 !rounded-md'
                            : ''}"
                    >
                        <SidebarItem
                            spanClass={activeUrl.includes("/admin/excel-upload/upload")
                                ? navigationSpanActiveClass
                                : navigationSpanNormalClass}
                            href={"/admin/excel-upload/upload"}
                            label="Excel Upload"
                            on:click={toggleSide}
                        >
                            <svelte:fragment slot="icon">
                                <img
                                    src="/images/sidebar-icons/purchase-stock.png"
                                    alt="Excel Upload"
                                    class="h-5"
                                />
                            </svelte:fragment>
                        </SidebarItem>
                        <a
                            href="/admin/excel-upload/upload"
                            class="absolute right-2 top-2 hidden group-hover:block"
                        >
                            <img src="/images/sidebar/add.svg" alt="add" />
                        </a>
                    </button>
                </div>
            </SidebarGroup>
        </SidebarWrapper>
    </Sidebar>
</Drawer>

<style>
    button.smooth:has(a:hover) {
        background-color: white;
    }

    :global(button.smooth li:hover span) {
        color: black;
    }

    :global(button.smooth a:hover ~ li span) {
        color: black;
    }

    :global(button.smooth li span) {
        color: white;
    }
</style>
