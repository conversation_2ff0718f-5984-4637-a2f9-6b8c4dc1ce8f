<script lang="ts">
    import CustomButton from "./admin/CustomButton.svelte";
    import CustomModal from "./admin/CustomModal.svelte";
    import SearchWithMultiSelect from "./SearchWithMultiSelect.svelte";
    import {
        getArrayDifference,
        getCombinations,
        recordDeleteKey,
        recordGet,
        recordHas,
        recordSet,
    } from "$lib/common/utils/common-utils";
    import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import RedRoundCloseButton from "./RedRoundCloseButton.svelte";

    export let onSelect = (data: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }) => {};

    let currentSelectedData: {
        attribute: IItemAttribute | null;
        attributeValue: IItemAttributeValue | null;
    } = {
        attribute: null,
        attributeValue: null,
    };
    let allSelectedData: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[] = [];

    let attributesData: IItemAttribute[] = [
        {
            id: 1,
            name: "color",
        },
        {
            id: 2,
            name: "size",
        },
        {
            id: 3,
            name: "density",
        },
    ];
    let attributeValuesData: IItemAttributeValue[] = [
        {
            id: 1,
            itemAttributeId: 1,
            value: "red",
            title: "Red",
        },
        {
            id: 2,
            itemAttributeId: 1,
            value: "black",
            title: "Black",
        },
        {
            id: 3,
            itemAttributeId: 1,
            value: "yellow",
            title: "Yellow",
        },
        {
            id: 4,
            itemAttributeId: 2,
            value: "small",
            title: "Small",
        },
        {
            id: 5,
            itemAttributeId: 2,
            value: "medium",
            title: "Medium",
        },
        {
            id: 6,
            itemAttributeId: 2,
            value: "large",
            title: "Large",
        },
        {
            id: 7,
            itemAttributeId: 3,
            value: "low",
            title: "Low",
        },
        {
            id: 8,
            itemAttributeId: 3,
            value: "medium",
            title: "Medium",
        },
    ];

    let isAttributeError = false;
    let isAttributeValueError = false;

    const _removeAttribute = (selected: IItemAttribute[]) => {
        allSelectedData = allSelectedData.filter(
            (data) => !selected.some((s) => s.id === data.attribute.id)
        );
    };
</script>

<div class="rounded-lg border border-dashed border-gray-500 bg-white p-[1rem]">
    <div class="flex items-center justify-between">
        <div>
            <SearchWithMultiSelect
                onchange={() => {}}
                label="Select Attribute"
                filteredData={attributesData.filter(
                    (data) => !allSelectedData.some((selected) => selected.attribute.id === data.id)
                )}
                fieldName={"options"}
                level={"name"}
                removeInput={(selected) => {
                    _removeAttribute(selected);
                }}
                searchInput={() => {}}
                selectedFunc={(e) => {
                    if (currentSelectedData.attribute && currentSelectedData.attribute.id === e.id)
                        return;
                    currentSelectedData = {
                        attribute: e,
                        attributeValue: null,
                    };
                    currentSelectedData = currentSelectedData;
                }}
            />
            {#if isAttributeError}
                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                    Please select an attribute
                </p>
            {/if}

            {#if currentSelectedData.attribute}
                <div class="mt-2 inline-flex items-center rounded-full bg-gray-100 px-3 py-1">
                    <span class="text-sm text-gray-800">{currentSelectedData.attribute.name}</span>
                    <button
                        aria-label="Remove this attribute"
                        class="ml-2 text-gray-500 hover:text-gray-700"
                        on:click={() => {
                            currentSelectedData = {
                                attribute: null,
                                attributeValue: null,
                            };
                        }}
                    >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                </div>
            {/if}
        </div>
        <!-- Generate dropdowns based on selected options -->
        {#if currentSelectedData.attribute}
            <div>
                <SearchWithMultiSelect
                    label={"Select " + currentSelectedData.attribute.name}
                    level="title"
                    filteredData={attributeValuesData.filter(
                        (item) => item.itemAttributeId === currentSelectedData.attribute!.id
                    )}
                    removeInput={(selected) => {}}
                    selectedFunc={(e) => {
                        const value = e as IItemAttributeValue;
                        if (
                            currentSelectedData.attributeValue &&
                            currentSelectedData.attributeValue.id === value.id
                        )
                            return;
                        currentSelectedData = {
                            attribute: currentSelectedData.attribute,
                            attributeValue: value,
                        };
                    }}
                />
                {#if isAttributeValueError}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        Please select an attribute value
                    </p>
                {/if}

                {#if currentSelectedData.attributeValue}
                    <div class="mt-2 inline-flex items-center rounded-full bg-gray-100 px-3 py-1">
                        <span class="text-sm text-gray-800">
                            {currentSelectedData.attributeValue.title}
                        </span>
                        <button
                            aria-label="Remove this attribute value"
                            class="ml-2 text-gray-500 hover:text-gray-700"
                            on:click={() => {
                                currentSelectedData.attributeValue = null;
                                currentSelectedData = currentSelectedData;
                            }}
                        >
                            <svg
                                class="h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        </button>
                    </div>
                {/if}
            </div>
            <CustomButton
                title="Add"
                cssClass="bg-black h-fit m-0"
                onClick={() => {
                    if (!currentSelectedData.attribute) {
                        isAttributeError = true;
                        return;
                    }
                    if (!currentSelectedData.attributeValue) {
                        isAttributeValueError = true;
                        return;
                    }
                    allSelectedData.push({
                        attribute: currentSelectedData.attribute,
                        attributeValue: currentSelectedData.attributeValue,
                    });
                    allSelectedData = allSelectedData;

                    onSelect({
                        attribute: currentSelectedData.attribute,
                        attributeValue: currentSelectedData.attributeValue,
                    });
                    currentSelectedData = {
                        attribute: null,
                        attributeValue: null,
                    };
                }}
            />
        {/if}
    </div>
    {#if allSelectedData.length > 0}
        <div class="text-lg font-semibold text-gray-700 mb-2">Selected Attributes</div>

        <div class="mt-4 grid grid-cols-6 gap-4">
            {#each allSelectedData as item}
                <div class="rounded-lg border border-gray-300 bg-white shadow-sm overflow-hidden">
                    <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
                        <span class="text-gray-800 font-semibold text-sm capitalize">
                            {item.attribute.name}
                        </span>
                        <RedRoundCloseButton
                            onClick={() => {
                                allSelectedData = allSelectedData.filter(
                                    (data) =>
                                        !(
                                            data.attribute.id === item.attribute.id &&
                                            data.attributeValue.id === item.attributeValue.id
                                        )
                                );
                            }}
                        />
                    </div>
                    <div class="p-4 flex justify-center items-center">
                        <span class="text-gray-900 text-base font-medium">
                            {item.attributeValue.title}
                        </span>
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>
