<script lang="ts">
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import RawMaterialForm from "$lib/raw_material/components/RawMaterialForm.svelte";
    import type { IRawMaterialOld } from "$lib/raw_material/models/IRawMaterial";
    import { RawMaterialUtils } from "$lib/raw_material/utils/RawMaterialUtils";
    import { RepoProvider } from "$lib/RepoProvider";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";

    export let isLabel: boolean = true;
    export let supplierId: number = -1;
    export let selectedSupplier: ISupplier | null = null;
    export let selected: IRawMaterialOld | null = null;
    export let onSelected: (data: IRawMaterialOld) => void;
    export let disabled: boolean = false;
    export let showAddNew: boolean = true;
    export let onCrossClick: () => void = () => {};

    let searchTerm: string = "";
    let fetchedData: IRawMaterialOld[] = [];
    let showAddNewModal: boolean = false;

    const getData = async () => {
        const res = await RepoProvider.rawMaterialRepo.searchVariation(
            searchTerm,
            supplierId === -1 ? undefined : supplierId
        );
        if (res.success) {
            fetchedData = res.data;
        }
        if (showAddNew && fetchedData.length === 0) {
            const obj = {
                id:-1,
                name:"",
            } as IRawMaterialOld;
            obj.name = "Add new";
            fetchedData = [obj];
        }
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search; // Update the search term
        debounceSearch(); // Call the debounced search
    };

    const addNew = () => {
        showAddNewModal = true;
    };
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    bind:searchTerm
    level="name"
    searchInput={doSearch}
    selected={selected?.name ?? null}
    selectedObj={selected}
    selectedFunc={async(data) => {
        fetchedData = [];
        searchTerm = "";
        if (data) {
            if (data.id === -1) {
                return addNew();
            }
        }
        onSelected(data);
    }}
    bind:filteredData={fetchedData}
    {onCrossClick}
/>

<CustomModal title="Add New RawMaterial" bind:showModal={showAddNewModal}>
    <!-- <RawMaterialForm
        isInsideModal={true}
        suppliers={selectedSupplier
            ? [
                  {
                      supplier: {
                          id: selectedSupplier.id,
                          title: selectedSupplier.name,
                      },
                      price: 0,
                      moq: 0,
                  },
              ]
            : []}
        onSubmitSuccess={(data) => {
            fetchedData = [];
            searchTerm = "";
            if (!selectedSupplier) {
                onSelected(data);
            }
            showAddNewModal = false;
        }}
    /> -->
</CustomModal>
