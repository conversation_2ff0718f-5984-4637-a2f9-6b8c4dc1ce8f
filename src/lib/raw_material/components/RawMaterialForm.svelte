<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import {
        type IRawMaterialAddRequest,
        type IRawMaterialFormState,
    } from "../models/IRawMaterial";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import { onMount } from "svelte";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import ItemUnitDropdown from "$lib/item_unit/components/ItemUnitDropdown.svelte";
    import ItemCategorySearch from "$lib/item_category/components/ItemCategorySearch.svelte";
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import RawMaterialVariationTable from "./RawMaterialVariationTable.svelte";

    export let formState: IRawMaterialFormState = RawMaterialUtils.emptyFormState;
    export let isInsideModal: boolean = false;

    let isDoingTask: boolean = false;
    let isLoadingUnits: boolean = true;
    let validationErrors: ValidationErrors = new Map();
    let variationErrors: IndexedValidationErrors = new Map();
    let itemUnits: IItemUnit[] = [];
    const handleGstPercentage = (e: Event) => {
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.rawMaterial.gstPercentage = isNaN(value) ? 0 : value;
    };
    const handleSubmit = async () => {
        const payload: IRawMaterialAddRequest = {
            rawMaterial: {
                name: formState.rawMaterial.name,
                unitId: formState.rawMaterial.unit?.id ?? -1,
                categoryId: formState.rawMaterial.category?.id ?? -1,
                hsn: formState.rawMaterial.hsn,
                gstPercentage: formState.rawMaterial.gstPercentage,
            },
            variations: formState.variations.map((item) => {
                return {
                    name: item.name,
                    sku: item.sku,
                    msq: item.msq,
                    moq: item.moq,
                    attributes: item.attributes.map((attribute) => {
                        return {
                            attributeValueId: attribute.attributeValueId,
                        };
                    }),
                    priceData: item.priceData.map((priceData) => {
                        return {
                            price: priceData.price,
                            supplierId: priceData.supplier?.id ?? -1,
                        };
                    }),
                };
            }),
        };
        validationErrors = RawMaterialUtils.validateCreate(payload);
        variationErrors = RawMaterialUtils.validateVariations(payload.variations);
        if (validationErrors.size !== 0 || variationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        
        alert("All valid")
        // isDoingTask = true;
        // let res = await PresenterProvider.rawMaterialPresenter.onSubmit(payload);
        // if (res.success) {
        //     showSuccessToast(`Done`);
        //     await goto("/admin/raw-materials");
        // } else {
        //     showErrorToast(res.message);
        //     isDoingTask = false;
        // }
    };

    const _loadUnits = async () => {
        const response = await PresenterProvider.rawMaterialPresenter.getItemUnits(1, 1000);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            itemUnits = response.data.data;
            if (itemUnits.length === 0) {
                showErrorToast("Please add at least one unit to add a raw material", 6);
                return goto("/admin/item-units/add");
            }
            formState.rawMaterial.unit = itemUnits[0];
            isLoadingUnits = false;
        }
    };
    onMount(() => {
        _loadUnits();
        formState.variations.push(RawMaterialUtils.getEmptyVariation());
    });
</script>

{#if isLoadingUnits}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class=" w-[90vw] p-2">
            {#if !isInsideModal}
                <div class=" flex items-center justify-between py-2">
                    <FormHeader label={"Add raw material"}></FormHeader>
                    <BreadCrumbs breadCrumbData={[]} />
                </div>
                <hr class="mb-5" />
            {/if}

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <ItemCategorySearch
                        onSelected={(data) => {
                            if (data) {
                                formState.rawMaterial.category = data;
                            } else {
                                formState.rawMaterial.category = null;
                            }
                        }}
                        selected={formState.rawMaterial.category}
                    />
                    {#if validationErrors.has("categoryId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("categoryId")}
                        </p>
                    {/if}
                </div>
            </div>

            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                        Name
                        {#if validationErrors.has("name")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="name"
                        placeholder="Name"
                        class="dark:bg-primary-700 {validationErrors.has('name')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={formState.rawMaterial.name}
                    />
                    {#if validationErrors.has("name")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("name")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="unit" class="mb-2 font-sans capitalize tracking-[0px]">
                        Unit
                        {#if validationErrors.has("name")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>

                    <ItemUnitDropdown
                        data={itemUnits}
                        id="unit"
                        cssClass="dark:bg-primary-700 {validationErrors.has('unitId')
                            ? 'border-red-500'
                            : ''}"
                        selectedValue={formState.rawMaterial.unit}
                        onSelected={(data) => {
                            formState.rawMaterial.unit = data;
                        }}
                    />

                    {#if validationErrors.has("unitId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("unitId")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="hsn" class="mb-2 font-sans capitalize tracking-[0px]">
                        HSN
                        {#if validationErrors.has("hsn")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="hsn"
                        placeholder="HSN"
                        class="uppercase   dark:bg-primary-700 {validationErrors.has('hsn')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={formState.rawMaterial.hsn}
                    />
                    {#if validationErrors.has("hsn")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("hsn")}
                        </p>
                    {/if}
                </div>
                <div>
                    <Label for="gstPercentage" class="mb-2 font-sans capitalize tracking-[0px]">
                        GST Percentage
                        {#if validationErrors.has("gstPercentage")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="number"
                        id="gstPercentage"
                        placeholder="GST Percentage"
                        class="dark:bg-primary-700 {validationErrors.has('gstPercentage')
                            ? 'border-red-500'
                            : ''}"
                        value={formState.rawMaterial.gstPercentage}
                        on:change={handleGstPercentage}
                    />
                    {#if validationErrors.has("gstPercentage")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("gstPercentage")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>
                <RawMaterialVariationTable
                    formState={formState}
                    variationErrors={variationErrors}
                    validationErrors={validationErrors}
                    formErrors={variationErrors}
                />
            <div class="m-2"></div>
            <div class="mt-5 flex w-full justify-between ">
                 <CustomButton
                    title="Add Variation"
                    cssClass="bg-blue-600 hover:bg-blue-700"
                    onClick={() => {
                        if (!formState.variations) {
                            formState.variations = [];
                        }
                        const newVariation = RawMaterialUtils.getEmptyVariation();
                        formState.variations.push(newVariation);
                        formState.variations = formState.variations;
                    }}
                />
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={"Save"}
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>
{/if}
