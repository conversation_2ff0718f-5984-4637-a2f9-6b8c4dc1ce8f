<script lang="ts">
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { CloseButton, Input } from "flowbite-svelte";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { IRawMaterialFormState } from "../models/IRawMaterial";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";
    import RedRoundCloseButton from "$lib/common/components/RedRoundCloseButton.svelte";

    export let formState: IRawMaterialFormState;
    export let variationErrors: IndexedValidationErrors = new Map();
    export let validationErrors: ValidationErrors = new Map();
    export let formErrors: IndexedValidationErrors = new Map();

    // Track expanded state for each variation
    let expandedVariations: boolean[] = [];

     const handleMsq = (e: Event, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[index].msq = isNaN(value) ? 0 : value;
    };

      const handleMOQ = (e: Event, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[index].moq = isNaN(value) ? 0 : value;
    };

    const assignSupplier = (supplier: ISupplier, VariationIndex: number, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        formState.variations[VariationIndex].priceData[index].supplier = supplier;
    };

    const handlePrice = (e: any, variationIndex: number, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[variationIndex].priceData[index].price = isNaN(value) ? 0 : value;
    };

    const handleAttributeSelection = (
        data: { attribute: any; attributeValue: any },
        variationIndex: number
    ) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }

        const existingAttributeIndex = formState.variations[variationIndex].attributes.findIndex(
            (attr) => attr.attributeValueId === data.attributeValue.id
        );

        if (existingAttributeIndex === -1) {
            formState.variations[variationIndex].attributes.push({
                attributeValueId: data.attributeValue.id,
            });
            formState.variations = formState.variations; 
        }
    };

    const toggleVariation = (index: number) => {
        expandedVariations[index] = !expandedVariations[index];
        expandedVariations = expandedVariations; 
    };

    $: if (formState.variations) {
        while (expandedVariations.length < formState.variations.length) {
            expandedVariations.push(true);
        }
        if (expandedVariations.length > formState.variations.length) {
            expandedVariations = expandedVariations.slice(0, formState.variations.length);
        }
    }

</script>

<table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
    <thead
        class="bg-gray-200 text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
    >
        <tr>
            <th scope="col" class="px-6 py-3 text-sm">Expand</th>
            <th scope="col" class="px-6 py-3 text-sm">Sr. No.</th>
            <th scope="col" class="px-6 py-3 text-sm">Name</th>
            <th scope="col" class="px-6 py-3 text-sm">SKU</th>
            <th scope="col" class="px-6 py-3 text-sm">MSQ</th>
            <th scope="col" class="px-6 py-3 text-sm">MOQ</th>
            <th scope="col" class="px-6 py-3 text-sm">Remove</th>
        </tr>
    </thead>
    <tbody>
        {#each formState.variations as variation, index}
            <tr
                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
            >
                <td class="px-4 py-3">
                    <button
                        type="button"
                        class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        on:click={() => toggleVariation(index)}
                        aria-label={expandedVariations[index] ? 'Collapse variation details' : 'Expand variation details'}
                        title={expandedVariations[index] ? 'Collapse' : 'Expand'}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-4 w-4 transition-transform duration-200 {expandedVariations[index] ? 'rotate-90' : ''}"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                            />
                        </svg>
                    </button>
                </td>
                <td class="px-6 py-3">{index + 1}.</td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="text"
                            id="name"
                            placeholder="Name"
                            class="uppercase dark:bg-primary-700 w-[150px] h-[40px] {validationErrors.has(
                                'name'
                            )
                                ? 'border-red-500'
                                : ''}"
                            bind:value={variation.name}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "name")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="text"
                            id="sku"
                            placeholder="SKU"
                            class="uppercase dark:bg-primary-700 w-[150px] h-[40px] {validationErrors.has(
                                'sku'
                            )
                                ? 'border-red-500'
                                : ''}"
                            bind:value={variation.sku}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "sku")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="number"
                            id="msq"
                            placeholder="MSQ"
                            class="dark:bg-primary-700  w-[150px] h-[40px] {validationErrors.has(
                                'msq'
                            )
                                ? 'border-red-500'
                                : ''}"
                            value={variation.msq}
                            on:input={(e) => handleMsq(e, index)}
                        />
                        {#if validationErrors.has("msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("msq")}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <Input
                        type="number"
                        class={" w-[200px] h-[40px]"}
                        placeholder={"MOQ"}
                        value={variation.moq}
                        on:change={(e) => {
                            handleMOQ(e, index);
                        }}
                    />
                </td>
                <td class="px-4 py-2">
                    {#if formState.variations.length > 1}
                        <RedRoundCloseButton
                            onClick={() => {
                                formState.variations?.splice(index, 1);
                                formState.variations = formState.variations; // Trigger reactivity
                            }}
                        />
                    {/if}
                </td>
            </tr>

            {#if expandedVariations[index]}
                <tr>
                    <td colspan="7" class="p-0">
                        <div class="bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-600">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <div class="flex items-center gap-2 mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                    <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">Attributes</h4>
                                </div>
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                                    <AttributesSelection
                                        onSelect={(data) => {
                                            handleAttributeSelection(data, index);
                                        }}
                                    />
                                    {#if formErrors.has(index) && formErrors
                                            .get(index)!
                                            .find((item) => item.fieldName === "attributes")}
                                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                            {formErrors
                                                .get(index)!
                                                .find((item) => item.fieldName === "attributes")!.errorMessage}
                                        </p>
                                    {/if}
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center gap-2 mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">Supplier Information</h4>
                                </div>
                                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
                                    <table class="w-full">
                                        <thead class="bg-gray-100 dark:bg-gray-700">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">SR No.</th>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Supplier</th>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Price</th>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Remove</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {#if variation.priceData && variation.priceData.length > 0}
                                                {#each variation.priceData as priceItem, supplierIndex}
                                                    <tr class="border-t border-gray-200 dark:border-gray-600">
                                                        <td class="px-4 py-3 text-sm">
                                                            {supplierIndex + 1}
                                                        </td>
                                                        <td class="px-4 py-3">
                                                            <SupplierSearch
                                                                selected={priceItem.supplier}
                                                                onSelected={(data) => {
                                                                    assignSupplier(data, index, supplierIndex);
                                                                }}
                                                                isLabel={false}
                                                            />
                                                            {#if variationErrors.has(index) && variationErrors
                                                                    .get(index)!
                                                                    .find((item) => item.fieldName === "priceData.supplierId")}
                                                                <p class="pt-1 text-xs text-red-500">
                                                                    {variationErrors
                                                                        .get(index)!
                                                                        .find(
                                                                            (item) =>
                                                                                item.fieldName ===
                                                                                "priceData.supplierId"
                                                                        )!.errorMessage}
                                                                </p>
                                                            {/if}
                                                        </td>
                                                        <td class="px-4 py-3">
                                                            <Input
                                                                type="number"
                                                                class="w-full h-[36px]"
                                                                placeholder="Price"
                                                                value={priceItem.price}
                                                                on:change={(e) => {
                                                                    handlePrice(e, index, supplierIndex);
                                                                }}
                                                            />
                                                            {#if variationErrors.has(index) && variationErrors
                                                                    .get(index)!
                                                                    .find((item) => item.fieldName === "priceData.price")}
                                                                <p class="pt-1 text-xs text-red-500">
                                                                    {variationErrors
                                                                        .get(index)!
                                                                        .find(
                                                                            (item) =>
                                                                                item.fieldName ===
                                                                                "priceData.price"
                                                                        )!.errorMessage}
                                                                </p>
                                                            {/if}
                                                        </td>
                                                        <td class="px-4 py-3">
                                                            {#if variation.priceData && variation.priceData.length > 1}
                                                                <RedRoundCloseButton
                                                                    onClick={() => {
                                                                        variation.priceData?.splice(supplierIndex, 1);
                                                                        variation.priceData = variation.priceData;
                                                                    }}
                                                                />
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                {/each}
                                            {:else}
                                                <tr>
                                                    <td colspan="4" class="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                                                        No suppliers added yet. Click "Add New Supplier" below to add one.
                                                    </td>
                                                </tr>
                                            {/if}
                                        </tbody>
                                    </table>
                                </div>

                                <div class="mt-3 flex justify-end">
                                    <button
                                        type="button"
                                        class="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors shadow-sm"
                                        on:click={() => {
                                            if (!variation.priceData) {
                                                variation.priceData = [];
                                            }
                                            variation.priceData = [
                                                ...variation.priceData,
                                                { price: 0, supplier: null },
                                            ];
                                        }}
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 mr-2"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M12 4v16m8-8H4"
                                            />
                                        </svg>
                                        Add New Supplier
                                    </button>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            {/if}
        {/each}
    </tbody>
</table>
