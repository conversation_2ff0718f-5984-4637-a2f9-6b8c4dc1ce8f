import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { IRawMaterial, IRawMaterialAddRequest, IRawMaterialCreatePaylod, IRawMaterialUpdatePaylod } from "../models/IRawMaterial";

export interface IRawMaterialPresenter {
    getAll(page: number, pageSize: number,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IRawMaterial>>>;
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterial>>>;
    getById(id: number): Promise<DTO<IRawMaterial>>;
    onSubmit(payload: IRawMaterialAddRequest): Promise<DTO<IRawMaterial>>;
    onUpdate(payload: IRawMaterialUpdatePaylod): Promise<DTO<boolean>>;
    onDelete(ids: number[]): Promise<DTO<boolean>>;
    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>>;
}