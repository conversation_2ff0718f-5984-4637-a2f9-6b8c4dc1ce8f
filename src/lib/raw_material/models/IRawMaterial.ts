import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";
import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { ISupplier } from "$lib/supplier/models/ISupplier";

interface IRawMaterialAddFormState {

    rawMaterial: {
        category: IItemCategory | null;
        unit: IItemUnit | null;
        name: string;
        hsn: string;
        gstPercentage: number;
    },
    variations: IRawMaterialVariationAddState[];

}


interface IRawMaterialAddRequest {
    rawMaterial: {
        categoryId: number;
        unitId: number;
        name: string;
        hsn: string;
        gstPercentage: number;
    },
    variations: IRawMaterialVariationAddRequest[];
}

interface IRawMaterialVariationAddRequest {
    name: string;
    sku: string;
    msq: number;
    moq: number;
    attributes: {
        attributeValueId: number;
    }[];
    priceData: {
        supplierId: number;
        price: number;
    }[];
}

interface IRawMaterialVariationAddState {
    name: string;
    sku: string;
    msq: number;
    moq: number;
    attributes: {
        attributeValueId: number;
    }[];
    priceData: {
        supplier: ISupplier | null;
        price: number;
    }[];
}


interface IRawMaterialCreatePaylod {
    name: string;
    unitId: number;
    categoryId: number;
    hsn: string;
    gstPercentage: number;
    variations: ICreateVariation[];
}


interface IRawMaterialUpdatePaylod extends IRawMaterialCreatePaylod {
    id: number;
    status: RAW_MATERIAL_STAUS;
}

interface IRawMaterial extends BaseModel, DeletedMetaData, UpdatedMetaData {
    id: number;
    name: string;
    unit: IItemUnit;
    category: IItemCategory;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
    variations: ICreateVariation[];
}

interface ICreateVariation {
    enabled: boolean;
    hash: string;
    name: string;
    sku: string;
    msq: number;
    moq: number;
    priceData: {
        price: number;
        supplierId: number;
    }[];
}

interface ICreateRawMaterial {
    name: string;
    unitId: number;
    categoryId: number;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
}

enum RAW_MATERIAL_STAUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}

interface IRawMaterialPriceData {
    price: number;
    supplier: ISupplier | null;
}







// old
interface IRawMaterialOld extends BaseModel, DeletedMetaData, UpdatedMetaData {
    id: number;
    name: string;
    unitId: number;
    unitName: string;
    categoryId: number;
    categoryName: string;
    sku: string;
    msq: number;
    hsn: string;
    gstPercentage: number;
    price: number;
    status: RAW_MATERIAL_STAUS;
    priceData: IRawMaterialPriceDetailsOld[];

}

interface ICreateRawMaterialOld extends BaseModel, DeletedMetaData, UpdatedMetaData {
    name: string;
    unitId: number;
    categoryId: number;
    sku: string;
    msq: number;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
}


interface IRawMaterialPriceDataOld {

    price: number;
    moq: number;
    supplierId: number;
}

interface IRawMaterialPriceDetailsOld extends IRawMaterialPriceDataOld {
    supplier: string;
    rawMaterialId: number;
    rawMaterial: string;
}



export {
    type IRawMaterial, type ICreateRawMaterial, type ICreateVariation, RAW_MATERIAL_STAUS, type IRawMaterialAddFormState as IRawMaterialFormState, type IRawMaterialCreatePaylod, type IRawMaterialPriceData, type IRawMaterialUpdatePaylod,
    type IRawMaterialOld, type ICreateRawMaterialOld,
    type IRawMaterialPriceDataOld, type IRawMaterialPriceDetailsOld,
    type IRawMaterialAddRequest,
    type IRawMaterialVariationAddRequest,
    type IRawMaterialVariationAddState,


};